@extends('layouts.dashboard')

@section('title', 'Dashboard - Sports Academy')

@section('header')
    <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
        <div class="flex items-center justify-between">
            <div>
                <nav class="text-sm text-gray-500 mb-2">
                    <a href="{{ route('dashboard') }}" class="hover:text-gray-700">Home</a>
                    <span class="mx-2">›</span>
                    <span class="text-gray-900">Dashboard</span>
                </nav>
                <h1 class="text-2xl font-bold text-gray-900">Welcome to Your Dashboard</h1>
                <p class="text-gray-600 mt-1">Manage your sports academy with ease and efficiency</p>
            </div>
            <div class="flex items-center space-x-4">
                <button
                    class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 flex items-center space-x-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                        </path>
                    </svg>
                    <span>Export Report</span>
                </button>
                <button class="bg-black text-white px-4 py-2 rounded-lg hover:bg-gray-800 flex items-center space-x-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    <span>QUICK ADD</span>
                </button>
            </div>
        </div>
    </div>
@endsection

@section('content')
    <div class="space-y-6">
        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
            @if (Auth::user()->role === 'admin' || Auth::user()->role === 'branch_manager')
                <!-- Total Branches -->
                <div class="stats-card scale-in" style="animation-delay: 0.1s;">
                    <div class="stats-icon">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                            </path>
                        </svg>
                    </div>
                    <div class="stats-value">{{ \App\Models\Branch::count() ?? 5 }}</div>
                    <div class="stats-label">TOTAL BRANCHES</div>
                    <div class="stats-change positive">
                        ↑ +12% from last month
                    </div>
                </div>
            @endif

            <!-- Total Academies -->
            <div class="stats-card scale-in" style="animation-delay: 0.2s;">
                <div class="stats-icon">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z">
                        </path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z">
                        </path>
                    </svg>
                </div>
                <div class="stats-value">
                    @if (Auth::user()->role === 'admin')
                        {{ \App\Models\Academy::count() ?? 12 }}
                    @elseif(Auth::user()->role === 'branch_manager')
                        {{ \App\Models\Academy::where('branch_id', Auth::user()->branch_id)->count() ?? 12 }}
                    @else
                        {{ Auth::user()->academy_id ? 1 : 12 }}
                    @endif
                </div>
                <div class="stats-label">TOTAL ACADEMIES</div>
                <div class="stats-change positive">
                    ↑ +8% from last month
                </div>
            </div>

            <!-- Active Players -->
            <div class="stats-card scale-in" style="animation-delay: 0.3s;">
                <div class="stats-icon">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
                        </path>
                    </svg>
                </div>
                <div class="stats-value">
                    @if (Auth::user()->role === 'admin')
                        {{ \App\Models\Student::count() ?? 248 }}
                    @elseif(Auth::user()->role === 'branch_manager')
                        {{ \App\Models\Student::where('branch_id', Auth::user()->branch_id)->count() ?? 248 }}
                    @else
                        {{ \App\Models\Student::where('academy_id', Auth::user()->academy_id)->count() ?? 248 }}
                    @endif
                </div>
                <div class="stats-label">ACTIVE PLAYERS</div>
                <div class="stats-change positive">
                    ↑ +15% from last month
                </div>
            </div>

            <!-- Monthly Revenue -->
            <div class="stats-card scale-in" style="animation-delay: 0.4s;">
                <div class="stats-icon">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z">
                        </path>
                    </svg>
                </div>
                <div class="stats-value">
                    @php
                        $query = \App\Models\Payment::whereMonth('payment_date', now()->month)->whereYear(
                            'payment_date',
                            now()->year,
                        );

                        if (Auth::user()->role === 'branch_manager') {
                            $query->where('branch_id', Auth::user()->branch_id);
                        } elseif (Auth::user()->role === 'academy_manager') {
                            $query->where('academy_id', Auth::user()->academy_id);
                        }

                        $monthlyRevenue = $query->sum('amount') ?? 45000;
                    @endphp
                    ${{ number_format($monthlyRevenue, 0) }}
                </div>
                <div class="stats-label">MONTHLY REVENUE</div>
                <div class="stats-change positive">
                    ↑ +22% from last month
                </div>
            </div>
        </div>

        <!-- Quick Actions and Recent Notifications -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Quick Actions -->
            <div class="quick-actions fade-in-up" style="animation-delay: 0.5s;">
                <h3>
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                    Quick Actions
                </h3>
                <div class="quick-actions-grid">
                    @if (Auth::user()->role === 'admin' || Auth::user()->role === 'branch_manager')
                        <a href="{{ route('branches.create') }}" class="quick-action-item">
                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                                </path>
                            </svg>
                            <span class="text">Add New Branch</span>
                        </a>
                    @endif
                    <a href="#" class="quick-action-item">
                        <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 14l9-5-9-5-9 5 9 5z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z">
                            </path>
                        </svg>
                        <span class="text">Create Academy</span>
                    </a>
                    <a href="#" class="quick-action-item">
                        <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z">
                            </path>
                        </svg>
                        <span class="text">Register Player</span>
                    </a>
                    <a href="#" class="quick-action-item">
                        <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                            </path>
                        </svg>
                        <span class="text">Generate Invoice</span>
                    </a>
                    <a href="#" class="quick-action-item">
                        <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                            </path>
                        </svg>
                        <span class="text">View Reports</span>
                    </a>
                    <a href="#" class="quick-action-item">
                        <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z">
                            </path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z">
                            </path>
                        </svg>
                        <span class="text">System Settings</span>
                    </a>
                </div>
            </div>

            <!-- Recent Notifications -->
            <div class="recent-notifications fade-in-up" style="animation-delay: 0.6s;">
                <h3>
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5z">
                        </path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                        </path>
                    </svg>
                    Recent Notifications
                </h3>

                <div class="notification-item warning">
                    <div class="notification-icon">
                        <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z">
                            </path>
                        </svg>
                    </div>
                    <div class="notification-content">
                        <div class="notification-title">3 overdue invoices</div>
                        <div class="notification-message">Require immediate attention</div>
                    </div>
                </div>

                <div class="notification-item info">
                    <div class="notification-icon">
                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="notification-content">
                        <div class="notification-title">5 new players registered</div>
                        <div class="notification-message">This week across all branches</div>
                    </div>
                </div>

                <div class="notification-item success">
                    <div class="notification-icon">
                        <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="notification-content">
                        <div class="notification-title">$15,000 collected</div>
                        <div class="notification-message">Monthly target 75% achieved</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="recent-activity fade-in-up" style="animation-delay: 0.7s;">
            <h3>
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Recent Activity
                <a href="#" class="text-sm text-blue-600 hover:text-blue-800 font-normal">View All</a>
            </h3>

            <div class="overflow-x-auto">
                <table class="activity-table">
                    <thead>
                        <tr>
                            <th>Activity</th>
                            <th>User</th>
                            <th>Branch</th>
                            <th>Time</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z">
                                            </path>
                                        </svg>
                                    </div>
                                    <span>New player registration</span>
                                </div>
                            </td>
                            <td>Ahmed Al-Rashid</td>
                            <td>Riyadh Branch</td>
                            <td>2 minutes ago</td>
                            <td><span class="activity-status completed">Completed</span></td>
                        </tr>
                        <tr>
                            <td>
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                                            </path>
                                        </svg>
                                    </div>
                                    <span>Invoice generated</span>
                                </div>
                            </td>
                            <td>System</td>
                            <td>Jeddah Branch</td>
                            <td>15 minutes ago</td>
                            <td><span class="activity-status sent">Sent</span></td>
                        </tr>
                        <tr>
                            <td>
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z">
                                            </path>
                                        </svg>
                                    </div>
                                    <span>Payment received</span>
                                </div>
                            </td>
                            <td>Sara Mohammed</td>
                            <td>Riyadh Branch</td>
                            <td>1 hour ago</td>
                            <td><span class="activity-status confirmed">Confirmed</span></td>
                        </tr>
                        <tr>
                            <td>
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-cyan-100 rounded-full flex items-center justify-center">
                                        <svg class="w-4 h-4 text-cyan-600" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M12 14l9-5-9-5-9 5 9 5z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z">
                                            </path>
                                        </svg>
                                    </div>
                                    <span>New academy created</span>
                                </div>
                            </td>
                            <td>Admin</td>
                            <td>Dammam Branch</td>
                            <td>3 hours ago</td>
                            <td><span class="activity-status active">Active</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

    </div>
@endsection

@push('scripts')
    <script>
        // Modal functionality placeholder
        function openModal(action, type, id = null) {
            console.log(`Opening ${action} modal for ${type}`, id);
            // This will be implemented when modal system is created
            alert(`${action.charAt(0).toUpperCase() + action.slice(1)} ${type} modal will be implemented soon!`);
        }

        // Currency formatting for AED
        function formatCurrency(amount) {
            if (isNaN(amount) || amount === '') return '';
            return `${parseFloat(amount).toLocaleString('en-US', {
                minimumFractionDigits: 0,
                maximumFractionDigits: 2
            })} AED`;
        }

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            // Add any dashboard-specific initialization here
            console.log('Bank-style dashboard loaded successfully');
        });
    </script>
@endpush
