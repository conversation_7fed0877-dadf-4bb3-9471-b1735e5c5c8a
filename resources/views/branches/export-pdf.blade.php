<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Branch Management Report - UAE English Sports Academy</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'IBM Plex Sans', Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            background: #fff;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #1f2937;
        }
        
        .logo {
            max-width: 150px;
            height: auto;
            margin-bottom: 10px;
        }
        
        .title {
            font-size: 24px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 5px;
        }
        
        .subtitle {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 10px;
        }
        
        .date {
            font-size: 12px;
            color: #9ca3af;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 12px;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        
        .table th,
        .table td {
            border: 1px solid #e5e7eb;
            padding: 8px;
            text-align: left;
            vertical-align: top;
        }
        
        .table th {
            background: #f9fafb;
            font-weight: bold;
            font-size: 11px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            color: #374151;
        }
        
        .table td {
            font-size: 11px;
        }
        
        .status-active {
            color: #059669;
            font-weight: bold;
        }
        
        .status-inactive {
            color: #dc2626;
            font-weight: bold;
        }
        
        .currency {
            font-weight: bold;
            color: #1f2937;
        }
        
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            text-align: center;
            font-size: 10px;
            color: #9ca3af;
        }
        
        .page-break {
            page-break-before: always;
        }
        
        @media print {
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            
            .page-break {
                page-break-before: always;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="title">UAE English Sports Academy</div>
        <div class="subtitle">Branch Management Report</div>
        <div class="date">Generated on {{ now()->format('F d, Y \a\t H:i:s') }}</div>
    </div>

    <!-- Statistics Summary -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-value">{{ $stats['total_branches'] }}</div>
            <div class="stat-label">Total Branches</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">{{ $stats['active_branches'] }}</div>
            <div class="stat-label">Active Branches</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">{{ $stats['inactive_branches'] }}</div>
            <div class="stat-label">Inactive Branches</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">{{ $stats['total_academies'] }}</div>
            <div class="stat-label">Total Academies</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">{{ $stats['total_students'] }}</div>
            <div class="stat-label">Total Students</div>
        </div>
        <div class="stat-card">
            <div class="stat-value currency">AED {{ number_format($stats['total_revenue'], 0) }}</div>
            <div class="stat-label">Total Revenue</div>
        </div>
    </div>

    <!-- Branches Table -->
    <table class="table">
        <thead>
            <tr>
                <th>ID</th>
                <th>Branch Name</th>
                <th>Location</th>
                <th>Contact</th>
                <th>Status</th>
                <th>Academies</th>
                <th>Students</th>
                <th>Revenue (AED)</th>
                <th>Created</th>
            </tr>
        </thead>
        <tbody>
            @foreach($branches as $branch)
                <tr>
                    <td>{{ $branch->id }}</td>
                    <td>{{ $branch->name }}</td>
                    <td>{{ $branch->location }}</td>
                    <td>
                        @if($branch->phone)
                            <div>📞 {{ $branch->formatted_phone ?? $branch->phone }}</div>
                        @endif
                        @if($branch->email)
                            <div>✉️ {{ $branch->email }}</div>
                        @endif
                    </td>
                    <td>
                        <span class="{{ $branch->status ? 'status-active' : 'status-inactive' }}">
                            {{ $branch->status ? 'Active' : 'Inactive' }}
                        </span>
                    </td>
                    <td>{{ $branch->academies_count }}</td>
                    <td>{{ $branch->students_count }}</td>
                    <td class="currency">
                        {{ number_format($branch->payments->where('status', 'completed')->sum('amount'), 0) }}
                    </td>
                    <td>{{ $branch->created_at->format('M d, Y') }}</td>
                </tr>
            @endforeach
        </tbody>
    </table>

    <!-- Summary Section -->
    @if($branches->count() > 0)
        <div style="margin-top: 30px; padding: 20px; background: #f9fafb; border-radius: 8px;">
            <h3 style="font-size: 16px; font-weight: bold; margin-bottom: 15px; color: #1f2937;">Report Summary</h3>
            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px;">
                <div>
                    <strong>Branch Distribution:</strong>
                    <ul style="margin-left: 20px; margin-top: 5px;">
                        <li>Active: {{ $stats['active_branches'] }} ({{ $stats['total_branches'] > 0 ? round(($stats['active_branches'] / $stats['total_branches']) * 100, 1) : 0 }}%)</li>
                        <li>Inactive: {{ $stats['inactive_branches'] }} ({{ $stats['total_branches'] > 0 ? round(($stats['inactive_branches'] / $stats['total_branches']) * 100, 1) : 0 }}%)</li>
                    </ul>
                </div>
                <div>
                    <strong>Performance Metrics:</strong>
                    <ul style="margin-left: 20px; margin-top: 5px;">
                        <li>Avg Academies per Branch: {{ $stats['total_branches'] > 0 ? round($stats['total_academies'] / $stats['total_branches'], 1) : 0 }}</li>
                        <li>Avg Students per Branch: {{ $stats['total_branches'] > 0 ? round($stats['total_students'] / $stats['total_branches'], 1) : 0 }}</li>
                        <li>Avg Revenue per Branch: AED {{ $stats['total_branches'] > 0 ? number_format($stats['total_revenue'] / $stats['total_branches'], 0) : 0 }}</li>
                    </ul>
                </div>
            </div>
        </div>
    @endif

    <!-- Footer -->
    <div class="footer">
        <p>UAE English Sports Academy - Branch Management System</p>
        <p>This report contains confidential information. Please handle with care.</p>
        <p>Report generated automatically on {{ now()->format('F d, Y \a\t H:i:s T') }}</p>
    </div>

    <script>
        // Auto-print when page loads (optional)
        // window.onload = function() { window.print(); };
    </script>
</body>
</html>
