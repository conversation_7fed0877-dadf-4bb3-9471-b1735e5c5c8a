@extends('layouts.dashboard')

@section('title', $branch->name . ' - Branch Details')

@section('header')
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">{{ $branch->name }}</h1>
            <p class="mt-1 text-sm text-gray-600">Branch details and management overview</p>
        </div>
        <div class="mt-4 lg:mt-0 flex space-x-3">
            @can('update', $branch)
                <a href="{{ route('branches.edit', $branch) }}" class="btn-bank-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    Edit Branch
                </a>
            @endcan
            <a href="{{ route('branches.index') }}" class="btn-bank-outline">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Branches
            </a>
        </div>
    </div>
@endsection

@section('content')
    <!-- Branch Status Banner -->
    <div class="mb-6">
        @if($branch->status)
            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span class="text-green-800 font-medium">This branch is currently active and operational</span>
                </div>
            </div>
        @else
            <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span class="text-red-800 font-medium">This branch is currently inactive</span>
                </div>
            </div>
        @endif
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bank-card">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Academies</p>
                    <p class="text-2xl font-bold text-purple-600">{{ $stats['total_academies'] }}</p>
                </div>
            </div>
        </div>

        <div class="bank-card">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Programs</p>
                    <p class="text-2xl font-bold text-blue-600">{{ $stats['total_programs'] }}</p>
                </div>
            </div>
        </div>

        <div class="bank-card">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Students</p>
                    <p class="text-2xl font-bold text-indigo-600">{{ $stats['total_students'] }}</p>
                    <p class="text-xs text-gray-500">{{ $stats['active_students'] }} active</p>
                </div>
            </div>
        </div>

        <div class="bank-card">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Revenue</p>
                    <p class="text-2xl font-bold text-green-600">AED {{ number_format($stats['total_revenue'], 0) }}</p>
                    <p class="text-xs text-gray-500">AED {{ number_format($stats['pending_payments'], 0) }} pending</p>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Branch Information -->
        <div class="lg:col-span-2">
            <div class="bank-card">
                <div class="border-b border-gray-200 pb-4 mb-6">
                    <h3 class="text-lg font-medium text-gray-900">Branch Information</h3>
                </div>

                <div class="space-y-6">
                    <!-- Basic Details -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Branch Name</label>
                            <p class="text-sm text-gray-900">{{ $branch->name }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $branch->status ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                {{ $branch->status ? 'Active' : 'Inactive' }}
                            </span>
                        </div>
                    </div>

                    <!-- Location Details -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Location</label>
                        <p class="text-sm text-gray-900">{{ $branch->location }}</p>
                    </div>

                    @if($branch->address)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Full Address</label>
                            <p class="text-sm text-gray-900">{{ $branch->address }}</p>
                        </div>
                    @endif

                    <!-- Contact Information -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        @if($branch->phone)
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                                <p class="text-sm text-gray-900">{{ $branch->formatted_phone ?? $branch->phone }}</p>
                            </div>
                        @endif

                        @if($branch->email)
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                                <p class="text-sm text-gray-900">{{ $branch->email }}</p>
                            </div>
                        @endif
                    </div>

                    <!-- Timestamps -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Created</label>
                            <p class="text-sm text-gray-900">{{ $branch->created_at->format('F d, Y \a\t H:i:s') }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Last Updated</label>
                            <p class="text-sm text-gray-900">{{ $branch->updated_at->format('F d, Y \a\t H:i:s') }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activities -->
            <div class="bank-card mt-8">
                <div class="border-b border-gray-200 pb-4 mb-6">
                    <h3 class="text-lg font-medium text-gray-900">Recent Activities</h3>
                </div>

                <div class="space-y-6">
                    <!-- Recent Students -->
                    @if($recentStudents->count() > 0)
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-3">Recent Student Enrollments</h4>
                            <div class="space-y-2">
                                @foreach($recentStudents as $student)
                                    <div class="flex items-center justify-between py-2 px-3 bg-gray-50 rounded-lg">
                                        <div>
                                            <p class="text-sm font-medium text-gray-900">{{ $student->name }}</p>
                                            <p class="text-xs text-gray-500">{{ $student->phone ?? 'No phone' }}</p>
                                        </div>
                                        <div class="text-right">
                                            <p class="text-xs text-gray-500">{{ $student->created_at->diffForHumans() }}</p>
                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium {{ $student->status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}">
                                                {{ ucfirst($student->status) }}
                                            </span>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <!-- Recent Payments -->
                    @if($recentPayments->count() > 0)
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-3">Recent Payments</h4>
                            <div class="space-y-2">
                                @foreach($recentPayments as $payment)
                                    <div class="flex items-center justify-between py-2 px-3 bg-gray-50 rounded-lg">
                                        <div>
                                            <p class="text-sm font-medium text-gray-900">{{ $payment->student->name ?? 'Unknown Student' }}</p>
                                            <p class="text-xs text-gray-500">Payment ID: {{ $payment->id }}</p>
                                        </div>
                                        <div class="text-right">
                                            <p class="text-sm font-bold text-gray-900">AED {{ number_format($payment->amount, 0) }}</p>
                                            <p class="text-xs text-gray-500">{{ $payment->created_at->diffForHumans() }}</p>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Quick Actions Sidebar -->
        <div class="lg:col-span-1">
            <div class="bank-card">
                <div class="border-b border-gray-200 pb-4 mb-6">
                    <h3 class="text-lg font-medium text-gray-900">Quick Actions</h3>
                </div>

                <div class="space-y-3">
                    @can('update', $branch)
                        <button onclick="toggleBranchStatus({{ $branch->id }})" 
                                class="w-full btn-bank-outline">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l4-4 4 4m0 6l-4 4-4-4"></path>
                            </svg>
                            {{ $branch->status ? 'Deactivate' : 'Activate' }} Branch
                        </button>
                    @endcan

                    <a href="{{ route('branches.export.pdf', ['branch_id' => $branch->id]) }}" 
                       class="w-full btn-bank-outline">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                        </svg>
                        Export Branch Report
                    </a>

                    <a href="{{ route('academies.index', ['branch_id' => $branch->id]) }}" 
                       class="w-full btn-bank-outline">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                        </svg>
                        View Academies
                    </a>

                    <a href="{{ route('students.index', ['branch_id' => $branch->id]) }}" 
                       class="w-full btn-bank-outline">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                        View Students
                    </a>

                    <a href="{{ route('payments.index', ['branch_id' => $branch->id]) }}" 
                       class="w-full btn-bank-outline">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                        </svg>
                        View Payments
                    </a>
                </div>
            </div>

            <!-- Branch Performance -->
            <div class="bank-card mt-6">
                <div class="border-b border-gray-200 pb-4 mb-6">
                    <h3 class="text-lg font-medium text-gray-900">Performance Metrics</h3>
                </div>

                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Academy Utilization</span>
                        <span class="text-sm font-medium text-gray-900">{{ $stats['total_academies'] > 0 ? round(($stats['active_students'] / ($stats['total_academies'] * 50)) * 100, 1) : 0 }}%</span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Revenue per Student</span>
                        <span class="text-sm font-medium text-gray-900">AED {{ $stats['total_students'] > 0 ? number_format($stats['total_revenue'] / $stats['total_students'], 0) : 0 }}</span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Programs per Academy</span>
                        <span class="text-sm font-medium text-gray-900">{{ $stats['total_academies'] > 0 ? round($stats['total_programs'] / $stats['total_academies'], 1) : 0 }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
// Status toggle functionality (reuse from index page)
window.toggleBranchStatus = function(branchId) {
    if (!confirm('Are you sure you want to change the status of this branch?')) {
        return;
    }

    fetch(`/branches/${branchId}/toggle-status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Failed to update branch status: ' + (data.message || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the branch status.');
    });
};
</script>
@endpush
