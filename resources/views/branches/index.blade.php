@extends('layouts.dashboard')

@section('title', 'Branch Management - Sports Academy')

@section('header')
    <div class="bg-gradient-to-r from-blue-600 to-indigo-700 rounded-2xl p-8 text-white shadow-xl">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div class="flex items-center space-x-4">
                <div class="bg-white/20 backdrop-blur-sm rounded-xl p-3">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                        </path>
                    </svg>
                </div>
                <div>
                    <h1 class="text-3xl font-bold text-white">Branch Management</h1>
                    <p class="mt-2 text-blue-100">Manage all academy branches, locations, and operations</p>
                    <div class="flex items-center mt-3 space-x-4 text-sm text-blue-100">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16"></path>
                            </svg>
                            {{ $filteredStats['total_branches'] }} Total Branches
                        </div>
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            {{ $filteredStats['active_branches'] }} Active
                        </div>
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2"></path>
                            </svg>
                            AED {{ number_format($filteredStats['total_revenue'] ?? 0, 0) }} Revenue
                        </div>
                    </div>
                </div>
            </div>
            @can('create', App\Models\Branch::class)
                <div class="mt-6 lg:mt-0">
                    <a href="{{ route('branches.create') }}"
                        class="inline-flex items-center px-6 py-3 bg-white text-blue-600 font-semibold rounded-xl hover:bg-blue-50 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Add New Branch
                    </a>
                </div>
            @endcan
        </div>
    </div>
@endsection

@section('content')
    <!-- Premium Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
        <!-- Total Branches Card -->
        <div
            class="group relative bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 p-6 border border-gray-100 hover:border-blue-200 transform hover:-translate-y-2 overflow-hidden">
            <div
                class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-500/10 to-blue-600/5 rounded-full -mr-16 -mt-16">
            </div>
            <div class="relative">
                <div class="flex items-center justify-between mb-4">
                    <div
                        class="bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl p-3 shadow-lg group-hover:scale-110 transition-transform duration-300">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                            </path>
                        </svg>
                    </div>
                    <div class="text-right">
                        <p class="text-xs font-medium text-gray-500 uppercase tracking-wider">Total Branches</p>
                        <p class="text-3xl font-bold text-gray-900 mt-1" data-stat="total_branches">
                            {{ $filteredStats['total_branches'] }}</p>
                    </div>
                </div>
                <div class="flex items-center justify-between">
                    <span
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z">
                            </path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        All Locations
                    </span>
                    <div class="text-xs text-gray-400">
                        <svg class="w-4 h-4 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Branches Card -->
        <div
            class="group relative bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 p-6 border border-gray-100 hover:border-green-200 transform hover:-translate-y-2 overflow-hidden">
            <div
                class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-green-500/10 to-green-600/5 rounded-full -mr-16 -mt-16">
            </div>
            <div class="relative">
                <div class="flex items-center justify-between mb-4">
                    <div
                        class="bg-gradient-to-br from-green-500 to-green-600 rounded-xl p-3 shadow-lg group-hover:scale-110 transition-transform duration-300">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="text-right">
                        <p class="text-xs font-medium text-gray-500 uppercase tracking-wider">Active Branches</p>
                        <p class="text-3xl font-bold text-gray-900 mt-1" data-stat="active_branches">
                            {{ $filteredStats['active_branches'] }}</p>
                    </div>
                </div>
                <div class="flex items-center justify-between">
                    <span
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <div class="w-2 h-2 bg-green-500 rounded-full mr-1 animate-pulse"></div>
                        Operational
                    </span>
                    <div class="text-xs text-green-500">
                        <svg class="w-4 h-4 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- Inactive Branches Card -->
        <div
            class="group relative bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 p-6 border border-gray-100 hover:border-red-200 transform hover:-translate-y-2 overflow-hidden">
            <div
                class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-red-500/10 to-red-600/5 rounded-full -mr-16 -mt-16">
            </div>
            <div class="relative">
                <div class="flex items-center justify-between mb-4">
                    <div
                        class="bg-gradient-to-br from-red-500 to-red-600 rounded-xl p-3 shadow-lg group-hover:scale-110 transition-transform duration-300">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="text-right">
                        <p class="text-xs font-medium text-gray-500 uppercase tracking-wider">Inactive Branches</p>
                        <p class="text-3xl font-bold text-gray-900 mt-1" data-stat="inactive_branches">
                            {{ $filteredStats['inactive_branches'] }}</p>
                    </div>
                </div>
                <div class="flex items-center justify-between">
                    <span
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z">
                            </path>
                        </svg>
                        Suspended
                    </span>
                    <div class="text-xs text-red-500">
                        <svg class="w-4 h-4 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Academies Card -->
        <div
            class="group relative bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 p-6 border border-gray-100 hover:border-purple-200 transform hover:-translate-y-2 overflow-hidden">
            <div
                class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-purple-500/10 to-purple-600/5 rounded-full -mr-16 -mt-16">
            </div>
            <div class="relative">
                <div class="flex items-center justify-between mb-4">
                    <div
                        class="bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl p-3 shadow-lg group-hover:scale-110 transition-transform duration-300">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253">
                            </path>
                        </svg>
                    </div>
                    <div class="text-right">
                        <p class="text-xs font-medium text-gray-500 uppercase tracking-wider">Total Academies</p>
                        <p class="text-3xl font-bold text-gray-900 mt-1" data-stat="total_academies">
                            {{ $filteredStats['total_academies'] }}</p>
                    </div>
                </div>
                <div class="flex items-center justify-between">
                    <span
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10">
                            </path>
                        </svg>
                        Programs
                    </span>
                    <div class="text-xs text-purple-500">
                        <svg class="w-4 h-4 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Students Card -->
        <div
            class="group relative bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 p-6 border border-gray-100 hover:border-indigo-200 transform hover:-translate-y-2 overflow-hidden">
            <div
                class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-indigo-500/10 to-indigo-600/5 rounded-full -mr-16 -mt-16">
            </div>
            <div class="relative">
                <div class="flex items-center justify-between mb-4">
                    <div
                        class="bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-xl p-3 shadow-lg group-hover:scale-110 transition-transform duration-300">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
                            </path>
                        </svg>
                    </div>
                    <div class="text-right">
                        <p class="text-xs font-medium text-gray-500 uppercase tracking-wider">Total Students</p>
                        <p class="text-3xl font-bold text-gray-900 mt-1" data-stat="total_students">
                            {{ $filteredStats['total_students'] }}</p>
                    </div>
                </div>
                <div class="flex items-center justify-between">
                    <span
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        Enrolled
                    </span>
                    <div class="text-xs text-indigo-500">
                        <svg class="w-4 h-4 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Search and Filter Section -->
    <div class="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 mb-8" x-data="{ showAdvanced: false }">
        <form method="GET" action="{{ route('branches.index') }}" id="searchForm" class="space-y-6">
            <!-- Search Header -->
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg p-2">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Search & Filter Branches</h3>
                        <p class="text-sm text-gray-500">Find branches by name, location, or other criteria</p>
                    </div>
                </div>
                <div class="flex items-center space-x-2">
                    @if (request()->hasAny([
                            'search',
                            'status',
                            'date_from',
                            'date_to',
                            'min_academies',
                            'max_academies',
                            'min_students',
                            'max_students',
                            'location_filter',
                            'sort_by',
                        ]))
                        <span
                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z">
                                </path>
                            </svg>
                            Filters Active
                        </span>
                    @endif
                </div>
            </div>

            <!-- Basic Search Row -->
            <div class="grid grid-cols-1 lg:grid-cols-12 gap-4">
                <!-- Search Input -->
                <div class="lg:col-span-5">
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-2">Search Branches</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                        <input type="text" name="search" id="search" value="{{ request('search') }}"
                            class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white"
                            placeholder="Search by name, location, address, phone, or email...">
                    </div>
                </div>

                <!-- Status Filter -->
                <div class="lg:col-span-2">
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select name="status" id="status"
                        class="block w-full py-3 px-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white">
                        <option value="">All Status</option>
                        <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active Only</option>
                        <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive Only
                        </option>
                    </select>
                </div>

                <!-- Per Page -->
                <div class="lg:col-span-2">
                    <label for="per_page" class="block text-sm font-medium text-gray-700 mb-2">Show</label>
                    <select name="per_page" id="per_page"
                        class="block w-full py-3 px-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white">
                        <option value="15" {{ request('per_page', 15) == 15 ? 'selected' : '' }}>15 per page</option>
                        <option value="25" {{ request('per_page') == 25 ? 'selected' : '' }}>25 per page</option>
                        <option value="50" {{ request('per_page') == 50 ? 'selected' : '' }}>50 per page</option>
                        <option value="100" {{ request('per_page') == 100 ? 'selected' : '' }}>100 per page</option>
                    </select>
                </div>

                <!-- Action Buttons -->
                <div class="lg:col-span-3 flex items-end space-x-2">
                    <button type="button" @click="showAdvanced = !showAdvanced"
                        class="flex-1 inline-flex items-center justify-center px-4 py-3 border border-gray-300 rounded-xl text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                        :class="{ 'bg-blue-50 border-blue-300 text-blue-700': showAdvanced }">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4">
                            </path>
                        </svg>
                        <span x-text="showAdvanced ? 'Hide' : 'Advanced'"></span>
                    </button>

                    <button type="submit"
                        class="flex-1 inline-flex items-center justify-center px-4 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl hover:from-blue-700 hover:to-blue-800 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        Search
                    </button>
                </div>
            </div>

            <!-- Advanced Filters -->
            <div x-show="showAdvanced" x-transition class="border-t border-gray-200 pt-4">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <!-- Date Range -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Date From</label>
                        <input type="date" name="date_from" value="{{ request('date_from') }}" class="input-bank">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Date To</label>
                        <input type="date" name="date_to" value="{{ request('date_to') }}" class="input-bank">
                    </div>

                    <!-- Academy Count Range -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Min Academies</label>
                        <input type="number" name="min_academies" value="{{ request('min_academies') }}"
                            class="input-bank" min="0" placeholder="0">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Max Academies</label>
                        <input type="number" name="max_academies" value="{{ request('max_academies') }}"
                            class="input-bank" min="0" placeholder="Any">
                    </div>

                    <!-- Student Count Range -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Min Students</label>
                        <input type="number" name="min_students" value="{{ request('min_students') }}"
                            class="input-bank" min="0" placeholder="0">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Max Students</label>
                        <input type="number" name="max_students" value="{{ request('max_students') }}"
                            class="input-bank" min="0" placeholder="Any">
                    </div>

                    <!-- Location Filter -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Locations</label>
                        <select name="location_filter" class="input-bank">
                            <option value="">All Locations</option>
                            @foreach ($availableLocations as $location)
                                <option value="{{ $location }}"
                                    {{ request('location_filter') === $location ? 'selected' : '' }}>
                                    {{ $location }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Sort Options -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
                        <select name="sort_by" class="input-bank">
                            <option value="name" {{ request('sort_by', 'name') === 'name' ? 'selected' : '' }}>Name
                            </option>
                            <option value="location" {{ request('sort_by') === 'location' ? 'selected' : '' }}>Location
                            </option>
                            <option value="created_at" {{ request('sort_by') === 'created_at' ? 'selected' : '' }}>Date
                                Created</option>
                            <option value="updated_at" {{ request('sort_by') === 'updated_at' ? 'selected' : '' }}>Last
                                Updated</option>
                            <option value="academies_count"
                                {{ request('sort_by') === 'academies_count' ? 'selected' : '' }}>Academy Count</option>
                            <option value="students_count"
                                {{ request('sort_by') === 'students_count' ? 'selected' : '' }}>Student Count</option>
                        </select>
                    </div>
                </div>

                <div class="flex justify-between items-center mt-4 pt-4 border-t border-gray-200">
                    <div class="flex space-x-2">
                        @if (request()->hasAny([
                                'search',
                                'status',
                                'date_from',
                                'date_to',
                                'min_academies',
                                'max_academies',
                                'min_students',
                                'max_students',
                                'location_filter',
                                'sort_by',
                            ]))
                            <a href="{{ route('branches.index') }}" class="btn-bank-outline">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                                Clear All Filters
                            </a>
                        @endif
                    </div>

                    <div class="flex space-x-2">
                        <select name="sort_order" class="input-bank">
                            <option value="asc" {{ request('sort_order', 'asc') === 'asc' ? 'selected' : '' }}>
                                Ascending</option>
                            <option value="desc" {{ request('sort_order') === 'desc' ? 'selected' : '' }}>Descending
                            </option>
                        </select>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- Enhanced Bulk Actions and Export Section -->
    <div class="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 mb-8" x-data="bulkActions()">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <!-- Enhanced Bulk Actions -->
            <div class="flex items-center space-x-6">
                <div class="flex items-center space-x-3">
                    <div class="bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg p-2">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Bulk Actions</h3>
                        <p class="text-sm text-gray-500">Manage multiple branches at once</p>
                    </div>
                </div>

                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <input type="checkbox" id="selectAll" @change="toggleSelectAll"
                            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded transition-all duration-200">
                        <label for="selectAll" class="text-sm font-medium text-gray-700">
                            Select All
                        </label>
                        <span
                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                            x-show="selectedCount > 0">
                            <span x-text="selectedCount"></span> selected
                        </span>
                    </div>

                    <div x-show="selectedCount > 0" x-transition class="flex items-center space-x-3">
                        <select x-model="bulkAction"
                            class="block py-2 px-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 focus:bg-white text-sm">
                            <option value="">Choose Action</option>
                            <option value="activate">✅ Activate Selected</option>
                            <option value="deactivate">⏸️ Deactivate Selected</option>
                            <option value="delete">🗑️ Delete Selected</option>
                        </select>

                        <button type="button" @click="executeBulkAction" :disabled="!bulkAction || selectedCount === 0"
                            class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg hover:from-blue-700 hover:to-blue-800 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7">
                                </path>
                            </svg>
                            Execute Action
                        </button>
                    </div>
                </div>
            </div>

            <!-- Enhanced Export Options -->
            <div class="flex items-center space-x-4">
                <div class="flex items-center space-x-2">
                    <div class="bg-gradient-to-br from-green-500 to-green-600 rounded-lg p-2">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                            </path>
                        </svg>
                    </div>
                    <div>
                        <h4 class="text-sm font-semibold text-gray-900">Export Data</h4>
                        <p class="text-xs text-gray-500">Download reports</p>
                    </div>
                </div>

                <div class="flex items-center space-x-2">
                    <a href="{{ route('branches.export.excel', request()->query()) }}"
                        class="inline-flex items-center px-4 py-2 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 hover:text-green-800 transition-all duration-200 hover:scale-105 group">
                        <svg class="w-4 h-4 mr-2 group-hover:scale-110 transition-transform duration-200" fill="none"
                            stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                            </path>
                        </svg>
                        Excel
                    </a>
                    <a href="{{ route('branches.export.pdf', request()->query()) }}"
                        class="inline-flex items-center px-4 py-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 hover:text-red-800 transition-all duration-200 hover:scale-105 group">
                        <svg class="w-4 h-4 mr-2 group-hover:scale-110 transition-transform duration-200" fill="none"
                            stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z">
                            </path>
                        </svg>
                        PDF
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Premium Branches Table -->
    @include('branches._table', ['branches' => $branches])

    <!-- Pagination -->
    @if ($branches->hasPages())
        <div class="mt-6">
            {{ $branches->links() }}
        </div>
    @endif
@endsection

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-submit form on select change
            const selects = document.querySelectorAll(
                'select[name="status"], select[name="per_page"], select[name="sort_by"], select[name="sort_order"]'
            );
            selects.forEach(select => {
                select.addEventListener('change', function() {
                    this.form.submit();
                });
            });

            // Status toggle functionality
            window.toggleBranchStatus = function(branchId) {
                if (!confirm('Are you sure you want to change the status of this branch?')) {
                    return;
                }

                fetch(`/branches/${branchId}/toggle-status`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute(
                                'content')
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showNotification('Branch status updated successfully!', 'success');
                            setTimeout(() => location.reload(), 1000);
                        } else {
                            showNotification('Failed to update branch status: ' + (data.message ||
                                'Unknown error'), 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showNotification('An error occurred while updating the branch status.', 'error');
                    });
            };

            // Delete confirmation
            window.confirmDelete = function(form) {
                if (confirm(
                        'Are you sure you want to delete this branch? This action may deactivate the branch if it has associated data.'
                    )) {
                    form.submit();
                }
                return false;
            };

            // Real-time statistics update
            function updateStatistics() {
                fetch('/api/branches/statistics')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            updateStatisticsDisplay(data.stats);
                        }
                    })
                    .catch(error => console.error('Error updating statistics:', error));
            }

            function updateStatisticsDisplay(stats) {
                // Update statistics cards if they exist
                const statsElements = {
                    'total_branches': document.querySelector('[data-stat="total_branches"]'),
                    'active_branches': document.querySelector('[data-stat="active_branches"]'),
                    'inactive_branches': document.querySelector('[data-stat="inactive_branches"]'),
                    'total_academies': document.querySelector('[data-stat="total_academies"]'),
                    'total_students': document.querySelector('[data-stat="total_students"]')
                };

                Object.keys(statsElements).forEach(key => {
                    if (statsElements[key] && stats[key] !== undefined) {
                        statsElements[key].textContent = stats[key];
                    }
                });
            }

            // Update statistics every 30 seconds
            setInterval(updateStatistics, 30000);

            // Notification system
            window.showNotification = function(message, type = 'info') {
                const notification = document.createElement('div');
                notification.className =
                    `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;

                const bgColor = {
                    'success': 'bg-green-500',
                    'error': 'bg-red-500',
                    'warning': 'bg-yellow-500',
                    'info': 'bg-blue-500'
                } [type] || 'bg-blue-500';

                notification.className += ` ${bgColor} text-white`;
                notification.innerHTML = `
                    <div class="flex items-center">
                        <span class="mr-2">${message}</span>
                        <button onclick="this.parentElement.parentElement.remove()" class="ml-2 text-white hover:text-gray-200">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                `;

                document.body.appendChild(notification);

                // Animate in
                setTimeout(() => {
                    notification.classList.remove('translate-x-full');
                }, 100);

                // Auto remove after 5 seconds
                setTimeout(() => {
                    notification.classList.add('translate-x-full');
                    setTimeout(() => notification.remove(), 300);
                }, 5000);
            };
        });

        // Bulk Actions Alpine.js Component
        function bulkActions() {
            return {
                selectedBranches: [],
                bulkAction: '',

                get selectedCount() {
                    return this.selectedBranches.length;
                },

                toggleSelectAll() {
                    const checkboxes = document.querySelectorAll('input[name="branch_ids[]"]');
                    const selectAllCheckbox = document.getElementById('selectAll');

                    if (selectAllCheckbox.checked) {
                        this.selectedBranches = Array.from(checkboxes).map(cb => {
                            cb.checked = true;
                            return cb.value;
                        });
                    } else {
                        this.selectedBranches = [];
                        checkboxes.forEach(cb => cb.checked = false);
                    }
                },

                toggleBranch(branchId) {
                    const index = this.selectedBranches.indexOf(branchId);
                    if (index > -1) {
                        this.selectedBranches.splice(index, 1);
                    } else {
                        this.selectedBranches.push(branchId);
                    }

                    // Update select all checkbox
                    const checkboxes = document.querySelectorAll('input[name="branch_ids[]"]');
                    const selectAllCheckbox = document.getElementById('selectAll');
                    selectAllCheckbox.checked = this.selectedBranches.length === checkboxes.length;
                    selectAllCheckbox.indeterminate = this.selectedBranches.length > 0 && this.selectedBranches.length <
                        checkboxes.length;
                },

                executeBulkAction() {
                    if (!this.bulkAction || this.selectedBranches.length === 0) {
                        return;
                    }

                    const actionText = {
                        'activate': 'activate',
                        'deactivate': 'deactivate',
                        'delete': 'delete'
                    } [this.bulkAction];

                    if (!confirm(
                            `Are you sure you want to ${actionText} ${this.selectedBranches.length} selected branches?`)) {
                        return;
                    }

                    fetch('/branches/bulk-action', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute(
                                    'content')
                            },
                            body: JSON.stringify({
                                action: this.bulkAction,
                                branch_ids: this.selectedBranches
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                showNotification(data.message, 'success');
                                setTimeout(() => location.reload(), 1500);
                            } else {
                                showNotification('Failed to perform bulk action: ' + (data.message || 'Unknown error'),
                                    'error');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            showNotification('An error occurred while performing the bulk action.', 'error');
                        });
                }
            };
        }
    </script>
@endpush
