@extends('layouts.dashboard')

@section('title', 'Create New Branch - Sports Academy')

@section('header')
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Create New Branch</h1>
            <p class="mt-1 text-sm text-gray-600">Add a new academy branch to expand your operations</p>
        </div>
        <div class="mt-4 lg:mt-0">
            <a href="{{ route('branches.index') }}" class="btn-bank-outline">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Branches
            </a>
        </div>
    </div>
@endsection

@section('content')
    <div class="max-w-4xl mx-auto">
        <form method="POST" action="{{ route('branches.store') }}" class="space-y-8" id="branchForm">
            @csrf
            
            <!-- Basic Information -->
            <div class="bank-card">
                <div class="border-b border-gray-200 pb-4 mb-6">
                    <h3 class="text-lg font-medium text-gray-900">Basic Information</h3>
                    <p class="mt-1 text-sm text-gray-600">Enter the basic details for the new branch</p>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Branch Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                            Branch Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="name" id="name" value="{{ old('name') }}" 
                               class="input-bank @error('name') border-red-300 @enderror" 
                               placeholder="e.g., Dubai Marina Branch" required>
                        @error('name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Status -->
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                            Status
                        </label>
                        <select name="status" id="status" class="input-bank @error('status') border-red-300 @enderror">
                            <option value="1" {{ old('status', '1') == '1' ? 'selected' : '' }}>Active</option>
                            <option value="0" {{ old('status') == '0' ? 'selected' : '' }}>Inactive</option>
                        </select>
                        @error('status')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Location Information -->
            <div class="bank-card">
                <div class="border-b border-gray-200 pb-4 mb-6">
                    <h3 class="text-lg font-medium text-gray-900">Location Information</h3>
                    <p class="mt-1 text-sm text-gray-600">Specify the branch location and address details</p>
                </div>

                <div class="space-y-6">
                    <!-- Location -->
                    <div>
                        <label for="location" class="block text-sm font-medium text-gray-700 mb-2">
                            Location <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="location" id="location" value="{{ old('location') }}" 
                               class="input-bank @error('location') border-red-300 @enderror" 
                               placeholder="e.g., Dubai Marina, Dubai" required>
                        <p class="mt-1 text-sm text-gray-500">Enter the general location or area of the branch</p>
                        @error('location')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Full Address -->
                    <div>
                        <label for="address" class="block text-sm font-medium text-gray-700 mb-2">
                            Full Address
                        </label>
                        <textarea name="address" id="address" rows="3" 
                                  class="input-bank @error('address') border-red-300 @enderror" 
                                  placeholder="Enter the complete address including building name, street, area, and emirate">{{ old('address') }}</textarea>
                        <p class="mt-1 text-sm text-gray-500">Optional: Provide detailed address for better location identification</p>
                        @error('address')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="bank-card">
                <div class="border-b border-gray-200 pb-4 mb-6">
                    <h3 class="text-lg font-medium text-gray-900">Contact Information</h3>
                    <p class="mt-1 text-sm text-gray-600">Add contact details for the branch</p>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Phone Number -->
                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                            Phone Number
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <span class="text-gray-500 text-sm">+971</span>
                            </div>
                            <input type="tel" name="phone" id="phone" value="{{ old('phone') }}" 
                                   class="input-bank pl-12 @error('phone') border-red-300 @enderror" 
                                   placeholder="50 123 4567" pattern="[0-9]{8,9}">
                        </div>
                        <p class="mt-1 text-sm text-gray-500">Enter UAE phone number without country code</p>
                        @error('phone')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Email Address -->
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                            Email Address
                        </label>
                        <input type="email" name="email" id="email" value="{{ old('email') }}" 
                               class="input-bank @error('email') border-red-300 @enderror" 
                               placeholder="<EMAIL>">
                        <p class="mt-1 text-sm text-gray-500">Optional: Branch-specific email address</p>
                        @error('email')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex flex-col sm:flex-row sm:justify-end sm:space-x-4 space-y-3 sm:space-y-0">
                <a href="{{ route('branches.index') }}" class="btn-bank-outline">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Cancel
                </a>
                
                <button type="submit" class="btn-bank-primary" id="submitBtn">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    <span id="submitText">Create Branch</span>
                </button>
            </div>
        </form>
    </div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('branchForm');
    const submitBtn = document.getElementById('submitBtn');
    const submitText = document.getElementById('submitText');
    
    // Phone number formatting
    const phoneInput = document.getElementById('phone');
    phoneInput.addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, '');
        
        // Limit to 9 digits for UAE numbers
        if (value.length > 9) {
            value = value.substring(0, 9);
        }
        
        // Format the number
        if (value.length >= 6) {
            value = value.substring(0, 2) + ' ' + value.substring(2, 5) + ' ' + value.substring(5);
        } else if (value.length >= 3) {
            value = value.substring(0, 2) + ' ' + value.substring(2);
        }
        
        e.target.value = value;
    });

    // Form submission handling
    form.addEventListener('submit', function(e) {
        submitBtn.disabled = true;
        submitText.textContent = 'Creating Branch...';
        
        // Re-enable button after 5 seconds to prevent permanent disable on error
        setTimeout(() => {
            submitBtn.disabled = false;
            submitText.textContent = 'Create Branch';
        }, 5000);
    });

    // Auto-generate email suggestion based on branch name
    const nameInput = document.getElementById('name');
    const emailInput = document.getElementById('email');
    
    nameInput.addEventListener('blur', function() {
        if (!emailInput.value && nameInput.value) {
            const branchName = nameInput.value.toLowerCase()
                .replace(/[^a-z0-9\s]/g, '')
                .replace(/\s+/g, '');
            
            if (branchName) {
                emailInput.placeholder = `${branchName}@sportsacademy.ae`;
            }
        }
    });

    // Form validation enhancement
    const requiredFields = form.querySelectorAll('[required]');
    
    requiredFields.forEach(field => {
        field.addEventListener('blur', function() {
            if (!this.value.trim()) {
                this.classList.add('border-red-300');
            } else {
                this.classList.remove('border-red-300');
            }
        });
    });
});
</script>
@endpush
