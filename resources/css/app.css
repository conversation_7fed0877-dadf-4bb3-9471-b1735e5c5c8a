/* Font Imports - Must come first */
@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Branch Management Styles */
@import 'branch-management.css';

/* UAE English Sports Academy - Bank-Style Dashboard CSS */

/* CSS Variables for Bank-Style Design */
:root {
    /* Brand Colors */
    --leaders-red: #E53E3E;
    --deep-red: #C53030;
    --gold-yellow: #D69E2E;

    /* Neutral Colors */
    --pure-white: #FFFFFF;
    --off-white: #FAFAFA;
    --light-gray: #F7FAFC;
    --medium-gray: #E2E8F0;
    --dark-gray: #4A5568;
    --charcoal-black: #1A202C;

    /* Status Colors */
    --success-green: #38A169;
    --warning-orange: #DD6B20;
    --error-red: #E53E3E;
    --info-blue: #3182CE;

    /* Typography */
    --font-family-primary: 'Century Gothic', 'IBM Plex Sans', system-ui, sans-serif;
    --font-family-arabic: 'IBM Plex Sans Arabic', 'IBM Plex Sans', system-ui, sans-serif;

    /* Shadows */
    --shadow-card: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-hover: 0 4px 8px rgba(0, 0, 0, 0.15);
    --shadow-bank: 0 8px 32px rgba(0, 0, 0, 0.12);
    --shadow-sidebar: 2px 0 10px rgba(0, 0, 0, 0.1);

    /* Layout */
    --header-height: 64px;
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 64px;
    --footer-height: 60px;

    /* Border Radius */
    --radius-card: 8px;
    --radius-button: 6px;
    --radius-large: 12px;

    /* Spacing */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-xxl: 3rem;

    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* Base Styles */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family-primary);
    background-color: var(--off-white);
    color: var(--charcoal-black);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    margin: 0;
    padding: 0;
}

/* RTL Support */
[dir="rtl"] {
    direction: rtl;
    text-align: right;
}

[dir="rtl"] .sidebar {
    right: 0;
    left: auto;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
}

[dir="rtl"] .main-content {
    margin-right: var(--sidebar-width);
    margin-left: 0;
}

[dir="rtl"] .sidebar-collapsed .main-content {
    margin-right: var(--sidebar-collapsed-width);
    margin-left: 0;
}

/* Arabic Text */
[lang="ar"], .arabic-text {
    font-family: var(--font-family-arabic);
    direction: rtl;
}

/* Layout Structure */
.dashboard-layout {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.header {
    height: var(--header-height);
    background: var(--pure-white);
    border-bottom: 1px solid var(--medium-gray);
    box-shadow: var(--shadow-card);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    display: flex;
    align-items: center;
    padding: 0 var(--space-lg);
}

.sidebar {
    width: var(--sidebar-width);
    background: var(--pure-white);
    border-right: 1px solid var(--medium-gray);
    box-shadow: var(--shadow-sidebar);
    position: fixed;
    top: var(--header-height);
    left: 0;
    bottom: 0;
    z-index: 999;
    overflow-y: auto;
    transition: all var(--transition-normal);
}

.sidebar-collapsed {
    width: var(--sidebar-collapsed-width);
}

.main-content {
    margin-left: var(--sidebar-width);
    margin-top: var(--header-height);
    margin-bottom: var(--footer-height);
    padding: var(--space-lg);
    transition: all var(--transition-normal);
    min-height: calc(100vh - var(--header-height) - var(--footer-height));
}

.sidebar-collapsed .main-content {
    margin-left: var(--sidebar-collapsed-width);
}

.footer {
    height: var(--footer-height);
    background: var(--pure-white);
    border-top: 1px solid var(--medium-gray);
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 998;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 var(--space-lg);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.mobile-open {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    [dir="rtl"] .sidebar {
        transform: translateX(100%);
    }

    [dir="rtl"] .sidebar.mobile-open {
        transform: translateX(0);
    }

    [dir="rtl"] .main-content {
        margin-right: 0;
    }
}

/* Bank-Style Components */

/* Header Components */
.header-logo {
    height: 50px;
    width: auto;
    object-fit: contain;
    transition: all var(--transition-fast);
}

.header-logo:hover {
    transform: scale(1.05);
}

.header-brand {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: var(--charcoal-black);
    font-weight: 600;
    font-size: 1.125rem;
    transition: color var(--transition-fast);
}

.header-brand:hover {
    color: var(--leaders-red);
    text-decoration: none;
}

/* Remove header brand text styles since we're only using logo */

.header-actions {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    margin-left: auto;
}

[dir="rtl"] .header-actions {
    margin-left: 0;
    margin-right: auto;
}

/* Language Toggle */
.language-toggle {
    background: var(--light-gray);
    border: 1px solid var(--medium-gray);
    border-radius: var(--radius-button);
    padding: var(--space-sm) var(--space-md);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: 0.875rem;
    font-weight: 500;
}

.language-toggle:hover {
    background: var(--medium-gray);
    border-color: var(--leaders-red);
}

.language-toggle.active {
    background: var(--leaders-red);
    color: var(--pure-white);
    border-color: var(--leaders-red);
}

/* User Profile Dropdown */
.user-profile {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-button);
    cursor: pointer;
    transition: all var(--transition-fast);
    background: var(--light-gray);
    border: 1px solid var(--medium-gray);
}

.user-profile:hover {
    background: var(--medium-gray);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--leaders-red);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--pure-white);
    font-weight: 600;
    font-size: 0.875rem;
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

[dir="rtl"] .user-info {
    align-items: flex-end;
}

.user-name {
    font-weight: 600;
    font-size: 0.875rem;
    color: var(--charcoal-black);
    line-height: 1.2;
}

.user-role {
    font-size: 0.75rem;
    color: var(--dark-gray);
    line-height: 1.2;
}

/* Sidebar Components - Dark Theme */
.sidebar {
    background: #1a1a1a;
    border-right: 1px solid #333;
    color: #fff;
}

.sidebar-header {
    padding: var(--space-lg);
    border-bottom: 1px solid #333;
    background: #1a1a1a;
    color: #fff;
}

.sidebar-nav {
    padding: var(--space-md) 0;
}

.nav-item {
    margin: 0 var(--space-md) var(--space-sm);
}

.nav-link {
    display: flex;
    align-items: center;
    padding: var(--space-md);
    border-radius: var(--radius-button);
    text-decoration: none;
    color: #9ca3af;
    font-weight: 500;
    transition: all var(--transition-fast);
    position: relative;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    text-decoration: none;
}

.nav-link.active {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
}

.nav-link.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--leaders-red);
    border-radius: 0 var(--radius-button) var(--radius-button) 0;
}

[dir="rtl"] .nav-link.active::before {
    left: auto;
    right: 0;
    border-radius: var(--radius-button) 0 0 var(--radius-button);
}

.nav-icon {
    width: 20px;
    height: 20px;
    margin-right: var(--space-md);
    flex-shrink: 0;
}

[dir="rtl"] .nav-icon {
    margin-right: 0;
    margin-left: var(--space-md);
}

.nav-text {
    font-size: 0.875rem;
    transition: opacity var(--transition-fast);
}

.sidebar-collapsed .nav-text {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.sidebar-collapsed .nav-icon {
    margin-right: 0;
    margin-left: 0;
}

/* Sidebar Toggle */
.sidebar-toggle {
    position: absolute;
    top: 50%;
    right: -12px;
    transform: translateY(-50%);
    width: 24px;
    height: 24px;
    background: var(--pure-white);
    border: 1px solid var(--medium-gray);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-fast);
    z-index: 1001;
}

[dir="rtl"] .sidebar-toggle {
    right: auto;
    left: -12px;
}

.sidebar-toggle:hover {
    background: var(--leaders-red);
    color: var(--pure-white);
    border-color: var(--leaders-red);
}

.sidebar-toggle svg {
    width: 12px;
    height: 12px;
    transition: transform var(--transition-fast);
}

.sidebar-collapsed .sidebar-toggle svg {
    transform: rotate(180deg);
}

/* Bank-Style Cards */
.bank-card {
    background: var(--pure-white);
    border: 1px solid var(--medium-gray);
    border-radius: var(--radius-card);
    box-shadow: var(--shadow-card);
    padding: var(--space-lg);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.bank-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--leaders-red), var(--gold-yellow));
}

.bank-card:hover {
    box-shadow: var(--shadow-bank);
    transform: translateY(-2px);
}

/* Statistics Cards - Dark Theme */
.stats-card {
    background: #1a1a1a;
    border-radius: 12px;
    padding: 24px;
    color: #fff;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid #333;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.stats-card .stats-icon {
    width: 48px;
    height: 48px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
    color: #9ca3af;
}

.stats-card .stats-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: #fff;
    line-height: 1;
    margin-bottom: 8px;
}

.stats-card .stats-label {
    font-size: 0.875rem;
    color: #9ca3af;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
}

.stats-card .stats-change {
    font-size: 0.75rem;
    margin-top: 8px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.stats-card .stats-change.positive {
    color: #10b981;
}

.stats-card .stats-change.negative {
    color: #ef4444;
}

/* Quick Actions Section */
.quick-actions {
    background: var(--pure-white);
    border-radius: 12px;
    padding: 24px;
    border: 1px solid var(--medium-gray);
    box-shadow: var(--shadow-card);
}

.quick-actions h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--charcoal-black);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.quick-action-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    border: 1px solid var(--medium-gray);
    border-radius: 8px;
    text-decoration: none;
    color: var(--charcoal-black);
    transition: all 0.2s ease;
    background: var(--light-gray);
}

.quick-action-item:hover {
    background: var(--leaders-red);
    color: var(--pure-white);
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(229, 62, 62, 0.2);
}

.quick-action-item .icon {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
}

.quick-action-item .text {
    font-size: 0.875rem;
    font-weight: 500;
}

/* Recent Activity Section */
.recent-activity {
    background: var(--pure-white);
    border-radius: 12px;
    padding: 24px;
    border: 1px solid var(--medium-gray);
    box-shadow: var(--shadow-card);
}

.recent-activity h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--charcoal-black);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.activity-table {
    width: 100%;
    border-collapse: collapse;
}

.activity-table th {
    text-align: left;
    padding: 12px 16px;
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--dark-gray);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 1px solid var(--medium-gray);
}

.activity-table td {
    padding: 16px;
    border-bottom: 1px solid var(--light-gray);
    font-size: 0.875rem;
}

.activity-table tr:hover {
    background: var(--light-gray);
}

.activity-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: capitalize;
}

.activity-status.completed {
    background: #dcfce7;
    color: #166534;
}

.activity-status.sent {
    background: #dbeafe;
    color: #1e40af;
}

.activity-status.confirmed {
    background: #dcfce7;
    color: #166534;
}

.activity-status.active {
    background: #cffafe;
    color: #0f766e;
}

/* Recent Notifications Section */
.recent-notifications {
    background: var(--pure-white);
    border-radius: 12px;
    padding: 24px;
    border: 1px solid var(--medium-gray);
    box-shadow: var(--shadow-card);
}

.recent-notifications h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--charcoal-black);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 12px;
    transition: all 0.2s ease;
}

.notification-item:last-child {
    margin-bottom: 0;
}

.notification-item:hover {
    background: var(--light-gray);
}

.notification-item.warning {
    background: #fef3c7;
    border-left: 4px solid #f59e0b;
}

.notification-item.info {
    background: #dbeafe;
    border-left: 4px solid #3b82f6;
}

.notification-item.success {
    background: #dcfce7;
    border-left: 4px solid #10b981;
}

.notification-icon {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
    margin-top: 2px;
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--charcoal-black);
    margin-bottom: 4px;
}

.notification-message {
    font-size: 0.75rem;
    color: var(--dark-gray);
    line-height: 1.4;
}

/* Animation Classes */
.fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
}

.scale-in {
    animation: scaleIn 0.4s ease-out forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Mobile Responsive Adjustments */
@media (max-width: 768px) {
    .stats-card {
        padding: 20px;
    }

    .stats-card .stats-value {
        font-size: 2rem;
    }

    .quick-actions-grid {
        grid-template-columns: 1fr;
    }

    .activity-table {
        font-size: 0.75rem;
    }

    .activity-table th,
    .activity-table td {
        padding: 8px 12px;
    }
}

.bank-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-lg);
    padding-bottom: var(--space-md);
    border-bottom: 1px solid var(--light-gray);
}

.bank-card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--charcoal-black);
    margin: 0;
}

.bank-card-subtitle {
    font-size: 0.875rem;
    color: var(--dark-gray);
    margin: var(--space-xs) 0 0;
}

.bank-card-body {
    flex: 1;
}

.bank-card-footer {
    margin-top: var(--space-lg);
    padding-top: var(--space-md);
    border-top: 1px solid var(--light-gray);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* Stats Cards */
.stats-card {
    background: linear-gradient(135deg, var(--pure-white) 0%, var(--off-white) 100%);
    border: 1px solid var(--medium-gray);
    border-radius: var(--radius-large);
    padding: var(--space-xl);
    position: relative;
    overflow: hidden;
    transition: all var(--transition-normal);
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--leaders-red);
}

.stats-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-bank);
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-large);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--space-lg);
    background: linear-gradient(135deg, var(--leaders-red), var(--deep-red));
    color: var(--pure-white);
    font-size: 1.5rem;
}

.stats-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--charcoal-black);
    line-height: 1;
    margin-bottom: var(--space-sm);
}

.stats-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--dark-gray);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: var(--space-sm);
}

.stats-change {
    font-size: 0.75rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

.stats-change.positive {
    color: var(--success-green);
}

.stats-change.negative {
    color: var(--error-red);
}

/* Bank-Style Buttons */
.btn-bank {
    background: var(--leaders-red);
    color: var(--pure-white);
    border: 1px solid var(--leaders-red);
    border-radius: var(--radius-button);
    padding: var(--space-md) var(--space-lg);
    font-family: var(--font-family-primary);
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: inline-flex;
    align-items: center;
    gap: var(--space-sm);
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn-bank::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
}

.btn-bank:hover::before {
    left: 100%;
}

.btn-bank:hover {
    background: var(--deep-red);
    border-color: var(--deep-red);
    transform: translateY(-1px);
    box-shadow: var(--shadow-hover);
    color: var(--pure-white);
    text-decoration: none;
}

.btn-bank:active {
    transform: translateY(0);
}

.btn-bank-secondary {
    background: transparent;
    color: var(--leaders-red);
    border: 1px solid var(--leaders-red);
}

.btn-bank-secondary:hover {
    background: var(--leaders-red);
    color: var(--pure-white);
}

.btn-bank-outline {
    background: transparent;
    color: var(--dark-gray);
    border: 1px solid var(--medium-gray);
}

.btn-bank-outline:hover {
    background: var(--light-gray);
    border-color: var(--leaders-red);
    color: var(--leaders-red);
}

/* Bank-Style Forms */
.form-group {
    margin-bottom: var(--space-lg);
}

.form-label {
    display: block;
    font-weight: 600;
    font-size: 0.875rem;
    color: var(--charcoal-black);
    margin-bottom: var(--space-sm);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.form-control-bank {
    width: 100%;
    padding: var(--space-md);
    border: 1px solid var(--medium-gray);
    border-radius: var(--radius-button);
    font-family: var(--font-family-primary);
    font-size: 0.875rem;
    background: var(--pure-white);
    transition: all var(--transition-fast);
    outline: none;
}

.form-control-bank:focus {
    border-color: var(--leaders-red);
    box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
}

.form-control-bank:invalid {
    border-color: var(--error-red);
}

.form-control-bank:valid {
    border-color: var(--success-green);
}

/* Bank-Style Tables */
.table-bank {
    width: 100%;
    background: var(--pure-white);
    border-radius: var(--radius-card);
    overflow: hidden;
    box-shadow: var(--shadow-card);
    border-collapse: separate;
    border-spacing: 0;
}

.table-bank thead {
    background: linear-gradient(135deg, var(--charcoal-black), var(--dark-gray));
}

.table-bank thead th {
    padding: var(--space-lg);
    color: var(--pure-white);
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    border: none;
    text-align: left;
}

[dir="rtl"] .table-bank thead th {
    text-align: right;
}

.table-bank tbody tr {
    transition: all var(--transition-fast);
    border-bottom: 1px solid var(--light-gray);
}

.table-bank tbody tr:hover {
    background: var(--off-white);
    transform: scale(1.01);
}

.table-bank tbody tr:last-child {
    border-bottom: none;
}

.table-bank tbody td {
    padding: var(--space-lg);
    color: var(--charcoal-black);
    font-size: 0.875rem;
    border: none;
    vertical-align: middle;
}

/* Status Badges */
.badge-bank {
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-button);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    display: inline-flex;
    align-items: center;
    gap: var(--space-xs);
}

.badge-success {
    background: var(--success-green);
    color: var(--pure-white);
}

.badge-warning {
    background: var(--warning-orange);
    color: var(--pure-white);
}

.badge-error {
    background: var(--error-red);
    color: var(--pure-white);
}

.badge-info {
    background: var(--info-blue);
    color: var(--pure-white);
}

.badge-neutral {
    background: var(--medium-gray);
    color: var(--charcoal-black);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.scale-in {
    animation: scaleIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-in-left {
    animation: slideInLeft 0.3s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.3s ease-out;
}

.pulse {
    animation: pulse 2s infinite;
}

/* Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, var(--light-gray) 25%, var(--medium-gray) 50%, var(--light-gray) 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Mobile Overlay */
.mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 998;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.mobile-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
    :root {
        --space-lg: 1rem;
        --space-xl: 1.5rem;
    }

    .header {
        padding: 0 var(--space-md);
    }

    .header-brand-text {
        display: none;
    }

    .main-content {
        padding: var(--space-md);
    }

    .stats-card {
        padding: var(--space-lg);
    }

    .stats-value {
        font-size: 2rem;
    }

    .bank-card {
        padding: var(--space-md);
    }

    .table-bank {
        font-size: 0.75rem;
    }

    .table-bank thead th,
    .table-bank tbody td {
        padding: var(--space-md);
    }

    /* Mobile Card Layout for Tables */
    .table-mobile {
        display: none;
    }

    .card-mobile {
        display: block;
        background: var(--pure-white);
        border: 1px solid var(--medium-gray);
        border-radius: var(--radius-card);
        padding: var(--space-lg);
        margin-bottom: var(--space-md);
        box-shadow: var(--shadow-card);
    }

    .card-mobile-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: var(--space-md);
        padding-bottom: var(--space-md);
        border-bottom: 1px solid var(--light-gray);
    }

    .card-mobile-title {
        font-weight: 600;
        color: var(--charcoal-black);
    }

    .card-mobile-body {
        display: grid;
        gap: var(--space-sm);
    }

    .card-mobile-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--space-sm) 0;
    }

    .card-mobile-label {
        font-size: 0.75rem;
        font-weight: 600;
        color: var(--dark-gray);
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .card-mobile-value {
        font-size: 0.875rem;
        color: var(--charcoal-black);
        text-align: right;
    }

    [dir="rtl"] .card-mobile-value {
        text-align: left;
    }

    .card-mobile-actions {
        margin-top: var(--space-md);
        padding-top: var(--space-md);
        border-top: 1px solid var(--light-gray);
        display: flex;
        gap: var(--space-sm);
        flex-wrap: wrap;
    }
}

@media (max-width: 480px) {
    .header-actions {
        gap: var(--space-sm);
    }

    .user-info {
        display: none;
    }

    .stats-card {
        text-align: center;
    }

    .stats-icon {
        margin: 0 auto var(--space-md);
    }

    .btn-bank {
        padding: var(--space-sm) var(--space-md);
        font-size: 0.75rem;
    }
}

/* Print Styles */
@media print {
    .sidebar,
    .header,
    .footer,
    .sidebar-toggle,
    .mobile-overlay {
        display: none !important;
    }

    .main-content {
        margin: 0 !important;
        padding: 0 !important;
    }

    .bank-card,
    .stats-card {
        box-shadow: none !important;
        border: 1px solid var(--medium-gray) !important;
        break-inside: avoid;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --pure-white: #FFFFFF;
        --charcoal-black: #000000;
        --medium-gray: #666666;
        --leaders-red: #CC0000;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
