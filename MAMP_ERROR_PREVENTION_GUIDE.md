# MAMP Laravel Error Prevention Guide

## 🚨 Problem Solved

The error you encountered:
```
Warning: Unknown: Failed to open stream: No such file or directory in Unknown on line 0
Fatal error: Failed opening required '/Applications/MAMP/htdocs/cosemtics/vendor/laravel/framework/src/Illuminate/Foundation/Console/../resources/server.php'
```

**Root Cause**: Multiple PHP development servers were running simultaneously, trying to access non-existent project directories.

## ✅ Permanent Solution Applied

### 1. **Killed Problematic Processes**
- Terminated PHP servers pointing to `/Applications/MAMP/htdocs/cosemtics/`
- Cleared all conflicting Laravel development servers
- Cleaned up orphaned PHP processes

### 2. **Cleared All Laravel Caches**
- Configuration cache cleared
- Application cache cleared
- Route cache cleared
- View cache cleared
- Optimization cache cleared

### 3. **MAMP Services Restarted**
- Apache server restarted
- MySQL server restarted
- PHP-FPM processes refreshed

## 🛡️ Prevention Measures

### **Quick Fix Script**
Run the provided script whenever you encounter similar issues:
```bash
./fix-mamp-laravel-error.sh
```

### **Manual Prevention Steps**

#### 1. **Always Use Correct URLs**
- ✅ Correct: `http://localhost:8888/uae_english_sports_academy/public`
- ❌ Wrong: `http://localhost:8000` (development server)

#### 2. **Stop Development Servers Properly**
```bash
# If you accidentally start Laravel's built-in server, stop it with:
Ctrl + C

# Or kill all PHP processes:
pkill -f php
```

#### 3. **Check Running Processes**
```bash
# List all PHP processes:
ps aux | grep php

# Kill specific process:
kill [process_id]
```

#### 4. **MAMP Port Configuration**
- **Apache Port**: 8888
- **MySQL Port**: 8889
- **Nginx Port**: 7888 (if used)

#### 5. **Project Structure Verification**
```
/Applications/MAMP/htdocs/
├── uae_english_sports_academy/     ✅ Your project
│   ├── public/                     ✅ Web root
│   ├── vendor/                     ✅ Dependencies
│   └── .env                        ✅ Configuration
└── other_projects/                 ⚠️  Keep separate
```

## 🔧 Troubleshooting Commands

### **Clear Everything**
```bash
cd /Applications/MAMP/htdocs/uae_english_sports_academy
php artisan optimize:clear
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear
```

### **Restart MAMP**
```bash
/Applications/MAMP/bin/stop.sh
/Applications/MAMP/bin/start.sh
```

### **Check Application Status**
```bash
php artisan about
php artisan route:list
```

### **Fix Permissions**
```bash
chmod -R 755 storage
chmod -R 755 bootstrap/cache
chmod -R 755 public
```

## 🚀 Best Practices

### **1. Project Organization**
- Keep each Laravel project in separate directories
- Use descriptive folder names
- Avoid spaces in directory names

### **2. Development Workflow**
- Always use MAMP for local development
- Don't mix `php artisan serve` with MAMP
- Close unused projects properly

### **3. Environment Management**
- Keep `.env` files updated
- Use correct database credentials
- Set proper APP_URL values

### **4. Regular Maintenance**
```bash
# Weekly cleanup (add to cron or run manually):
./fix-mamp-laravel-error.sh
```

## 📱 Quick Reference

### **Access Your Application**
- **Main URL**: http://localhost:8888/uae_english_sports_academy/public
- **Dashboard**: http://localhost:8888/uae_english_sports_academy/public/dashboard
- **Branch Management**: http://localhost:8888/uae_english_sports_academy/public/branches

### **MAMP Control**
- **Start MAMP**: Open MAMP application → Start Servers
- **Stop MAMP**: MAMP application → Stop Servers
- **Restart**: Stop → Wait 5 seconds → Start

### **Emergency Commands**
```bash
# Nuclear option - kill all PHP processes:
sudo pkill -f php

# Restart MAMP completely:
/Applications/MAMP/bin/stop.sh && sleep 5 && /Applications/MAMP/bin/start.sh

# Reset Laravel completely:
php artisan optimize:clear && php artisan key:generate
```

## 🆘 When to Use the Fix Script

Run `./fix-mamp-laravel-error.sh` when you see:
- "Failed to open stream" errors
- "No such file or directory" errors
- Laravel trying to access wrong project paths
- Multiple PHP servers conflicting
- MAMP not responding properly

## 📞 Support

If issues persist after running the fix script:
1. Check MAMP logs: `/Applications/MAMP/logs/`
2. Verify project permissions
3. Ensure no other web servers are running (Apache, Nginx)
4. Contact the development team

---

**Status**: ✅ **RESOLVED** - Error permanently fixed and prevention measures implemented.
