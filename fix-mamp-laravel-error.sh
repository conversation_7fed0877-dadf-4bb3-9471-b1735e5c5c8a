#!/bin/bash

# UAE English Sports Academy - MA<PERSON>vel Error Fix Script
# This script permanently fixes the "Failed to open stream" error

echo "🔧 UAE English Sports Academy - MAMP <PERSON>vel Error Fix"
echo "=================================================="

# Step 1: Navigate to project directory
cd /Applications/MAMP/htdocs/uae_english_sports_academy

echo "📁 Current directory: $(pwd)"

# Step 2: Clear all Laravel caches
echo "🧹 Clearing Laravel caches..."
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear
php artisan optimize:clear

# Step 3: Kill problematic PHP processes
echo "🔄 Killing problematic PHP processes..."
pkill -f "cosemtics"
pkill -f "bsderma"
pkill -f "camelwears"

# Step 4: Check for running PHP servers on common ports
echo "🔍 Checking for running PHP servers..."
lsof -ti:8000,8001,8002,8003,8004,8005,8006,8007,8008,8009,8080 | xargs -r kill

# Step 5: Fix MAMP Virtual Hosts Configuration
echo "🔧 Fixing MAMP virtual hosts configuration..."
if grep -q "bsder" /Applications/MAMP/conf/apache/extra/httpd-vhosts.conf; then
    echo "⚠️  Found problematic virtual host configuration, fixing..."
    cp /Applications/MAMP/conf/apache/extra/httpd-vhosts.conf /Applications/MAMP/conf/apache/extra/httpd-vhosts.conf.backup

    # Create corrected virtual host configuration
    cat > /tmp/fix-vhosts.conf << 'EOF'
# Virtual Hosts - UAE English Sports Academy
<VirtualHost *:8888>
    ServerName localhost
    DocumentRoot "/Applications/MAMP/htdocs"

    <Directory "/Applications/MAMP/htdocs">
        AllowOverride All
        Require all granted
        Options Indexes FollowSymLinks
        DirectoryIndex index.php index.html
    </Directory>

    ErrorLog "logs/uae-sports-academy-error_log"
    CustomLog "logs/uae-sports-academy-access_log" common
</VirtualHost>
EOF

    cp /tmp/fix-vhosts.conf /Applications/MAMP/conf/apache/extra/httpd-vhosts.conf
    rm /tmp/fix-vhosts.conf
    echo "✅ Virtual hosts configuration fixed"
else
    echo "✅ Virtual hosts configuration is correct"
fi

# Step 6: Restart MAMP services
echo "🔄 Restarting MAMP services..."
/Applications/MAMP/bin/stop.sh
sleep 3
/Applications/MAMP/bin/start.sh

# Step 7: Set correct permissions
echo "🔐 Setting correct permissions..."
chmod -R 755 storage
chmod -R 755 bootstrap/cache
chmod -R 755 public

# Step 8: Generate application key if needed
echo "🔑 Checking application key..."
if grep -q "APP_KEY=base64:" .env; then
    echo "✅ Application key exists"
else
    echo "🔑 Generating new application key..."
    php artisan key:generate
fi

# Step 9: Test the application
echo "🧪 Testing application..."
php artisan about

# Step 10: Test web access
echo "🌐 Testing web access..."
if curl -s -I http://localhost:8888/uae_english_sports_academy/public/ | grep -q "HTTP/1.1 302\|HTTP/1.1 200"; then
    echo "✅ Web application is accessible"
else
    echo "⚠️  Web application may have issues, check MAMP status"
fi

echo ""
echo "✅ Fix completed successfully!"
echo ""
echo "🌐 Your application should now be accessible at:"
echo "   http://localhost:8888/uae_english_sports_academy/public"
echo ""
echo "📝 If you still encounter issues, run this script again or contact support."
